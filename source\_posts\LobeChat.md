---
title: LobeChat - 开源AI聊天应用与开发框架完全指南
date: 2025-06-08 02:05:00
cover: https://cdn4.winhlb.com/2025/06/08/68448d2e3187c.png
tags:
  - AI
  - 聊天应用
  - 开源
  - LLM
  - 大语言模型
  - ChatGPT
  - 人工智能
categories: [AI应用]
description: 全面介绍LobeChat开源AI聊天应用的功能特点、部署方法和使用技巧，包含详细的多模型支持说明和插件开发指南。
keywords: [LobeChat, AI聊天, ChatGPT, 开源AI, 大语言模型, 插件开发]
---

探索 LobeChat：现代化设计的开源聊天应用与开发框架

## 核心功能概览

LobeChat 是一个现代化设计的开源 ChatGPT/LLMs 聊天应用与开发框架，支持以下核心能力：

### 1. 多模态交互
- 思维链可视化(CoT)
- 分支对话管理
- 白板内容创作(Artifacts)
- 文件上传与知识库

### 2. 多模型支持
支持主流AI模型服务商，包括：

| 模型ID | 输入 | 输出 | 优化目标 |
|---------|------|------|---------|
| gemini-2.5-flash-preview-05-20 | 音频、图片、视频和文本 | 文本 | 自适应思维，成本效益 |
| gemini-2.5-flash-preview-native-audio-dialog | 音频、视频和文本 | 文本和音频交错 | 高质量对话式音频输出 |
| gemini-2.5-flash-preview-tts | 文本 | 音频 | 低延迟文字转语音 |
| gemini-2.5-pro-preview-06-05 | 音频、图片、视频和文本 | 文本 | 增强思考和推理能力 |
| gemini-2.0-flash | 音频、图片、视频和文本 | 文本 | 新一代功能与实时串流 |
| gemini-1.5-pro | 音频、图片、视频和文本 | 文本 | 复杂推理任务 |
| imagen-3.0-generate-002 | 文本 | 图片 | 先进图片生成 |
| veo-2.0-generate-001 | 文字、图片 | 视频 | 高质量视频生成 |

### 3. 扩展能力
- 插件系统(Tools Calling)
- 助手市场(GPTs)
- 本地/远程数据库支持
- 多用户管理

### 4. 用户体验
- 语音交互(TTS/STT)
- 文生图功能
- PWA支持
- 移动设备优化
- 主题自定义

## 部署与使用

### 部署方式
1. **Vercel一键部署**: [点击部署](https://github.com/lobelia-chat/lobe-chat)
2. **Docker运行**:
```bash
docker run -d -p 3000:3000 -e OPENAI_API_KEY=your-key lobechat/lobe-chat
```
3. **手动部署**: 克隆仓库后运行`pnpm install && pnpm dev`

### 典型应用场景
- 开发者: 代码辅助与调试
- 创作者: 内容生成与优化
- 教育者: 知识管理与教学辅助

### 快速开始

**访问地址:**
<div class="highlight-tools "><i class="anzhiyufont anzhiyu-icon-angle-down expand ${highlightShrinkClass}"></i><div class="code-lang">url</div><div class="copy-notice"></div><i class="anzhiyufont anzhiyu-icon-paste copy-button"></i></div>https://lobe.0407123.xyz

**登录密码:**
<div class="highlight-tools "><i class="anzhiyufont anzhiyu-icon-angle-down expand ${highlightShrinkClass}"></i><div class="code-lang">password</div><div class="copy-notice"></div><i class="anzhiyufont anzhiyu-icon-paste copy-button"></i></div>admin123

> 关注[官方博客](https://blog.lobe-chat.xyz)获取最新动态