---
title: URL复制功能测试
date: 2025-01-08 10:00:00
tags: [测试, 功能]
---

# URL复制功能测试

这是一个测试文章，用来验证博客中的URL复制功能是否正常工作。

## 普通文本中的URL

这里有一些普通文本中的URL链接：

访问我的博客：https://bk.0407123.xyz
GitHub仓库：https://github.com/example/repo
Google搜索：https://www.google.com

## 代码块中的URL

下面是一些代码块中的URL：

```bash
# 克隆仓库
git clone https://github.com/example/repo.git

# 访问网站
curl https://api.example.com/data

# 下载文件
wget https://example.com/file.zip
```

```javascript
// API调用示例
const apiUrl = 'https://jsonplaceholder.typicode.com/posts'
fetch(apiUrl)
  .then(response => response.json())
  .then(data => console.log(data))
```

## 内联代码中的URL

这是内联代码中的URL：`https://example.com/api/v1/users`

## 链接形式的URL

这是正常的链接：[点击访问](https://www.example.com)

## 测试说明

1. 普通文本中的URL应该显示复制按钮 📋
2. 代码块中的URL应该在右上角显示链接复制按钮 🔗
3. 点击复制按钮应该能够复制对应的URL
4. 复制成功后应该显示"已复制!"提示
