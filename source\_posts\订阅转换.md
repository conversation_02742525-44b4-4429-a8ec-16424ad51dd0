---
title: 订阅转换 - 在Cloudflare上高效管理节点订阅
date: 2025-06-08 01:31:35
tags:
- 网络代理
- 订阅转换
- Cloudflare
categories:
- 网络工具
cover: https://cdn4.winhlb.com/2025/06/08/68448d2e680df.png
description: Edge Sub是一个基于Cloudflare的订阅转换工具，支持多种代理协议和订阅格式转换，帮助用户高效管理节点订阅。
---
Edge Sub：在Cloudflare全球网络上转换您的节点订阅

你是否曾经遇到过在不同代理客户端之间切换，却发现每个客户端都支持不同的订阅格式的困扰？或者，你希望能够将各种节点订阅统一管理，并在全球范围内加速访问？那么，Edge Sub正是你所需要的工具！

Edge Sub是一个创新性的解决方案，它允许你在Cloudflare的全球网络上转换你的节点订阅。这意味着你可以享受到Cloudflare遍布全球的数据中心带来的低延迟和高可用性，同时灵活地管理你的代理节点。

## Edge Sub能做什么？

Edge Sub的核心功能是其强大的订阅转换能力。它支持多种节点类型和订阅格式，使得你可以轻松地将现有订阅转换为你需要的格式。

### 支持的节点类型：
Edge Sub支持目前主流的多种代理节点类型，包括：

- Hysteria 1 和 Hysteria 2: 经过全面测试，表现稳定。
- TUIC v5: 同样经过全面测试，可以放心使用。
- Shadowsocks: 支持UDP over TCP，进一步优化网络体验。
- Trojan: 兼容并经过测试。
- Vmess 和 Vless: 已支持，并经过部分测试。
- HTTP 和 Socks 5: 这些基础协议也得到了支持。

遗憾的是，目前Edge Sub暂不支持WireGuard、ShadowsocksR和SSH，但项目未来可能会考虑增加这些支持。

### 支持的订阅类型：
Edge Sub不仅支持多种节点类型，还能处理多种订阅格式的输入和输出，包括：

- ShareLink 集合 (Base64编码): 最常见的订阅格式之一，支持输入和输出。
- Clash Meta 配置 和 Clash 配置: 如果你使用Clash系列客户端，Edge Sub可以轻松地处理其配置。值得一提的是，Clash配置的输出会经过滤，只包含Edge Sub已支持的代理类型，以确保兼容性。
- Sing-Box 配置: 对于Sing-Box用户，Edge Sub也提供了支持。

## 如何使用Edge Sub？
Edge Sub提供了两种主要的使用方式：

1. **UI界面**: 最简单直观的方式，你只需将Edge Sub部署在Cloudflare Pages上，然后通过其Web界面进行操作，根据提示即可完成订阅转换。
2. **API端点**: 对于开发者或需要自动化处理的用户，Edge Sub提供了API端点。
   - `/sub/clash-meta`: 专门用于Clash Meta订阅的转换。你可以通过URL参数指定远程订阅地址、可选的远程配置文件（INI格式）以及是否强制刷新缓存。
   - `/sub/debug`: 这是一个仅供测试的调试端点，输出格式可能随时发生变化。

## 部署Edge Sub：简单便捷
将Edge Sub部署到Cloudflare Pages非常简单，只需几个步骤：

1. Fork Edge Sub的GitHub项目。
2. 登录你的Cloudflare账户，前往“Workers & Pages”部分。
3. 选择“Pages”，然后点击“连接到Git”，选择你Fork的Edge Sub项目。
4. 在构建设置中，将“Framework preset”设置为Astro，并将“Build command”编辑为`pnpm build:frontend`。
5. 点击“Save and Deploy”，等待部署完成。

### 可选：为远程规则添加缓存 (强烈推荐！)
为了进一步提升处理速度，Edge Sub推荐为远程规则添加KV缓存。这可以显著加快订阅转换的速度，尤其是在处理大型或频繁更新的订阅时。

1. 在Cloudflare的“Workers & Pages”部分，前往“KV”。
2. 创建一个新的KV命名空间，并为其命名。
3. 回到你的Edge Sub项目设置，在“Functions”下的“KV namespace bindings”中，添加一个新的绑定。
4. 将“Variable name”设置为`EdgeSubDB`，并选择你刚刚创建的KV命名空间。
5. 保存设置后，找到最近的部署，点击“Retry Deployment”重新部署项目即可。

## 使用场景示例

1. **多设备同步**：将同一订阅转换为不同客户端支持的格式，在手机、电脑等设备上使用
2. **协议升级**：将老旧协议节点转换为新协议，提升安全性和速度
3. **订阅过滤**：只保留特定地区或特定协议的节点，简化订阅内容

## 常见问题

**Q: Edge Sub会存储我的订阅内容吗？**
A: 不会，所有转换操作都在内存中完成，不会持久化存储您的订阅数据。

**Q: 转换后的订阅速度会变慢吗？**
A: 不会，Cloudflare的边缘计算节点会确保转换过程高效快速。

**Q: 如何确保订阅安全性？**
A: 建议使用HTTPS访问Edge Sub，并定期更换订阅链接。

## 总结

Edge Sub为管理和转换你的节点订阅提供了一个强大而灵活的解决方案。通过利用Cloudflare的全球网络，它不仅能提高你代理访问的速度和稳定性，还能大大简化不同客户端和协议之间的转换过程。如果你正在寻找一个高效、便捷的订阅管理工具，Edge Sub绝对值得一试！

## 相关资源

**Edge Sub GitHub项目：**
<div class="highlight-tools "><i class="anzhiyufont anzhiyu-icon-angle-down expand ${highlightShrinkClass}"></i><div class="code-lang">url</div><div class="copy-notice"></div><i class="anzhiyufont anzhiyu-icon-paste copy-button"></i></div>https://github.com/edgesub

**Cloudflare Workers文档：**
<div class="highlight-tools "><i class="anzhiyufont anzhiyu-icon-angle-down expand ${highlightShrinkClass}"></i><div class="code-lang">url</div><div class="copy-notice"></div><i class="anzhiyufont anzhiyu-icon-paste copy-button"></i></div>https://developers.cloudflare.com/workers/

**代理协议比较指南：**
<div class="highlight-tools "><i class="anzhiyufont anzhiyu-icon-angle-down expand ${highlightShrinkClass}"></i><div class="code-lang">url</div><div class="copy-notice"></div><i class="anzhiyufont anzhiyu-icon-paste copy-button"></i></div>https://example.com/proxy-comparison