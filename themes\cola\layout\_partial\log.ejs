<link rel="stylesheet" href="<%- url_for('css/partial/log.css') %>" />

<%
  const weekText = ['天', '一', '二', '三', '四', '五', '六']
  let logPages = site.pages.filter(item => {
    const dirs = item.source.split('/')
    return dirs[0] === 'log' && dirs[1] !== 'index.md'
  })
  logPages = logPages.data.sort((a, b) => {
    return b.date.unix() - a.date.unix()
  })
  logPages.forEach(item => {
    const day = new Date(item.date).getDay()
    item.week = '星期' + weekText[day]
  })
%>

<div class="log">
  <% logPages.forEach(item => { %>
    <div class="log-item">
      <h2 class="log-title">
        <span><%- date(item.date, 'YYYY-MM-DD HH:mm:ss') %></span>
        <span><%- item.week %></span>
      </h2>
      <h1 class="log-main-title"><a href="<%- url_for('./' + item.path) %>"><%- item.title %></a></h1>
      <div class="log-wrapper <%- item.content.length > theme.log_text_num && 'more' %>" style="max-height: <%- theme.log_text_num %>px;">
        <article class="article-content"><%- item.content %></article>
        <% if (item.content.length > theme.log_text_num) { %>
          <a href="<%- url_for('./' + item.path) %>" class="more-title">阅读更多</a> 
        <% } %>
      </div>
    </div>
  <% }) %>
  <% if (logPages.length == 0) { %>
    <%- partial('_partial/empty', { title: 'Hello World' }) %>
  <% } %>
</div>
