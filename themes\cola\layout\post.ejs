

<%if (page.path === 'categories/index.html') { %>
  <%- partial('_partial/category', { cache: !config.relative_link }) %>
<% } else if (page.path === 'tags/index.html') { %>
  <%- partial('_partial/tags', { cache: !config.relative_link }) %>
<% } else if (page.path === 'log/index.html') { %>
  <%- partial('_partial/log', { cache: !config.relative_link }) %>
<% } else if (page.path === 'link/index.html') { %>
  <%- partial('_partial/link', { cache: !config.relative_link }) %>
<% } else if (page.path === 'about/index.html') { %>
  <%- partial('_partial/about', { cache: !config.relative_link }) %>
<% } else if (page.path === 'tools/index.html') { %>
  <%- partial('_partial/tools', { cache: !config.relative_link }) %>
<% } else { %>
  <%- partial('_partial/article.ejs', { post: page, index: false }) %> 
<% } %>
