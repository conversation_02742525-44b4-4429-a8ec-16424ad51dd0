.highlight {
  padding: 0 10px;
}
/*

Atom One Light by <PERSON>
Original One Light Syntax theme from https://github.com/atom/one-light-syntax

base:    #fafafa
mono-1:  #383a42
mono-2:  #686b77
mono-3:  #a0a1a7
hue-1:   #0184bb
hue-2:   #4078f2
hue-3:   #a626a4
hue-4:   #50a14f
hue-5:   #e45649
hue-5-2: #c91243
hue-6:   #986801
hue-6-2: #c18401

*/

.highlight {
  color: #383a42;
  background: #fafafa;
}

.highlight .comment,
.highlight .quote {
  color: #a0a1a7;
  font-style: italic;
}

.highlight .doctag,
.highlight .keyword,
.highlight .formula {
  color: #a626a4;
}

.highlight .section,
.highlight .name,
.highlight .selector-tag,
.highlight .deletion,
.highlight .subst {
  color: #e45649;
}

.highlight .literal {
  color: #0184bb;
}

.highlight .string,
.highlight .regexp,
.highlight .addition,
.highlight .attribute,
.highlight .meta .highlight .string {
  color: #50a14f;
}

.highlight .attr,
.highlight .variable,
.highlight .template-variable,
.highlight .type,
.highlight .selector-class,
.highlight .selector-attr,
.highlight .selector-pseudo,
.highlight .number {
  color: #986801;
}

.highlight .symbol,
.highlight .bullet,
.highlight .link,
.highlight .meta,
.highlight .selector-id,
.highlight .title {
  color: #4078f2;
}

.highlight .built_in,
.highlight .title.class_,
.highlight .class .highlight .title {
  color: #c18401;
}

.highlight .emphasis {
  font-style: italic;
}

.highlight .strong {
  font-weight: bold;
}

.highlight .link {
  text-decoration: underline;
}
