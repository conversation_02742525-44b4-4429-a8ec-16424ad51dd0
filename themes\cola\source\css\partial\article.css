/* URL复制按钮样式 */
.copy-url-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  margin-left: 5px;
  color: #666;
  cursor: pointer;
  transition: all 0.3s;
}

.copy-url-btn:hover {
  color: #1890ff;
}

.copy-url-btn i {
  font-size: 14px;
}

.copy-notice {
  position: absolute;
  background: #1890ff;
  color: white;
  padding: 2px 5px;
  border-radius: 3px;
  font-size: 12px;
  transform: translateY(-100%);
  animation: fadeInOut 2s ease-in-out;
}

@keyframes fadeInOut {
  0% { opacity: 0; }
  20% { opacity: 1; }
  80% { opacity: 1; }
  100% { opacity: 0; }
}

/* 代码块URL复制按钮样式 */
.code-copy-container {
  position: absolute;
  top: 5px;
  right: 5px;
  display: flex;
  gap: 5px;
}

.copy-code-url-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background: rgba(0, 0, 0, 0.6);
  color: #fff;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.3s;
  position: relative;
}

.copy-code-url-btn:hover {
  background: rgba(24, 144, 255, 0.8);
}

/* 文章基础样式 */
.article-container {
  display: flex;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.article {
  flex: 1;
  min-width: 0;
}

.article-title {
  font-size: 28px;
  margin-bottom: 20px;
  color: #333;
}

.article-info {
  margin-bottom: 30px;
  color: #666;
  font-size: 14px;
}

.article-info--item {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.article-info--info {
  flex: 1;
}

.article-info--categories,
.article-info--tags {
  display: inline-block;
  margin-right: 15px;
}

.article-info--date {
  display: inline-block;
  margin-right: 15px;
}

.article-cover {
  width: 120px;
  height: 80px;
  object-fit: cover;
  border-radius: 4px;
}

.article-content {
  line-height: 1.8;
  color: #333;
}

.article-content p {
  margin: 15px 0;
}

.article-catelogue {
  width: 280px;
  margin-left: 40px;
}

.article-catelogue--wrapper {
  position: sticky;
  top: 20px;
}

.catelogue {
  background: #fff;
  border-radius: 4px;
  padding: 15px;
  margin-bottom: 20px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.catelogue h3 {
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 16px;
}

.read-nums {
  margin: 30px 0;
  color: #666;
  font-size: 14px;
}

.comments-intro {
  margin: 30px 0 15px;
}

.comments-intro h2 {
  font-size: 20px;
  margin-bottom: 10px;
}

.comments-intro p {
  color: #666;
  font-size: 14px;
  margin: 0;
}